<template>
    <div class="search-panel-container">
        <!-- 顶部导航栏 -->
        <div class="top-nav">
            <div class="nav-items">
                <div
                    v-for="item in regularNavItems"
                    :key="item.name.toString()"
                    class="nav-item"
                    :class="{ active: item.name === activeName }"
                    @click="handleNavClick(item)"
                >
                    {{ item.name }}
                </div>
            </div>
            <el-select
                v-if="selectedNavItems.length > 0"
                v-model="selectedNavValue"
                placeholder="其他"
                size="small"
                class="nav-select"
                @change="handleSelectChange"
            >
                <el-option
                    v-for="item in selectedNavItems"
                    :key="item.name.toString()"
                    :label="item.name"
                    :value="item.name"
                    :class="{ active: item.name === activeName }"
                >
                </el-option>
            </el-select>
            <slot name="leftNav"></slot>

            <el-badge :is-dot="isDot" class="item">
                <el-button class="search-icon" @click="toggleSearchPanel">
                    <i class="el-icon-search"></i>
                    <span>搜索</span>
                </el-button>
            </el-badge>
            <div class="right-nav">
                <slot name="rightNav"></slot>
            </div>
        </div>

        <!-- 搜索面板 -->
        <el-collapse-transition>
            <!-- 注意：外层的div不能设置margin或者padding，
             否则会先将高度降低至0，再处理padding或者margin，这样就导致动画卡顿 -->
            <div v-show="isSearchPanelVisible" class="search-panel">
                <snbc-form
                    :form="queryParams"
                    :config="snbcFormConfig"
                    class="snbc-form"
                >
                    <template slot="form-body">
                        <el-col
                            v-for="(item, index) in queryItems"
                            :key="index"
                            class="form-item-height"
                            :span="8"
                        >
                            <snbc-form-item :config="item" />
                        </el-col>
                    </template>
                </snbc-form>
                <div class="button-group">
                    <el-button type="primary" @click="handleSearch"
                        >搜索</el-button
                    >
                    <el-button @click="handleReset">重置</el-button>
                </div>

                <div class="collapse-icon" @click="toggleSearchPanel">
                    <i
                        :class="
                            isSearchPanelVisible
                                ? 'el-icon-arrow-up'
                                : 'el-icon-arrow-down'
                        "
                    ></i>
                </div>
            </div>
        </el-collapse-transition>
    </div>
</template>

<script>
import SnbcForm from 'snbcCommon/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'snbcCommon/components/snbc-form/SnbcFormItem.vue';

export default {
    name: 'CollapsibleSearchPanel',
    components: {
        SnbcForm,
        SnbcFormItem
    },
    props: {
        // 按钮是否展示红点
        isDot: {
            type: Boolean,
            default: false
        },
        // 导航栏
        navItems: {
            type: Array,
            default: () => []
        },
        // 导航栏的点击查询条件
        value: {
            type: String,
            default: ''
        },
        // 默认打开
        defaultOpen: {
            type: Boolean,
            default: false
        },
        // 查询参数对象
        queryParams: {
            type: Object,
            default() {
                return {};
            }
        },
        // 查询区域表单配置
        queryConfig: {
            type: Object,
            default() {
                return {
                    elFormAttrs: {},
                    items: []
                };
            }
        }
    },
    data() {
        return {
            isSearchPanelVisible: this.defaultOpen,
            // 使用 value prop 初始化 activeName
            activeName: this.value,
            selectedNavValue: ''
        };
    },

    computed: {
        // 普通导航项（不需要放在下拉框中的）
        regularNavItems() {
            return this.navItems.filter((item) => !item.selected);
        },
        // 需要放在下拉框中的导航项
        selectedNavItems() {
            return this.navItems.filter((item) => item.selected === true);
        },
        // SnbcForm组件配置
        snbcFormConfig() {
            return {
                elFormAttrs: this.elFormAttrs
            };
        },
        // ElForm组件应用属性
        elFormAttrs() {
            return {
                ...this.defaultElFromAttrs,
                ...(this.queryConfig.elFormAttrs || {})
            };
        },
        // 查询条件的表单项
        queryItems() {
            return (this.queryConfig.items || []).map((item, index) => {
                // 扩展表单项的配置
                return this.expandItem(item);
            });
        }
    },
    watch: {
        // 监听 value 的变化以同步外部变化
        value(newVal) {
            this.activeName = newVal;
            // 更新下拉框的值
            if (this.selectedNavItems.some((item) => item.name === newVal)) {
                this.selectedNavValue = newVal;
            }
        }
    },
    methods: {
        toggleSearchPanel() {
            this.isSearchPanelVisible = !this.isSearchPanelVisible;
        },
        handleNavClick(item) {
            this.activeName = item.name;

            // 同步下拉框的值
            if (item.selected) {
                this.selectedNavValue = item.name;
            } else {
                // 如果点击的是普通导航项，清空下拉框选择
                this.selectedNavValue = '';
            }
            // 触发 input 事件以支持 v-model
            this.$emit('input', item.name);
            // 保持原有的 navChange 事件
            this.$emit('navChange', item);
        },
        handleSearch() {
            this.$emit('search');
        },
        handleReset() {
            this.$emit('reset');
        },
        // 不同表单项执行不同的配置扩展
        expandItem(item) {
            return this.expandSnbcFormCommon(item);
        },
        // 扩展组件属性
        expandSnbcFormCommon(item) {
            return {
                modelObj: this.queryParams,
                elFormItemAttrs: {
                    label: item.name,
                    ...(item.elFormItemAttrs || {})
                },
                label: item.name,
                ...item
            };
        },
        // 处理下拉框选择变化
        handleSelectChange(value) {
            const selectedItem = this.selectedNavItems.find(
                (item) => item.name === value
            );
            if (selectedItem) {
                this.handleNavClick(selectedItem);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.search-panel-container {
    width: 100%;
}
.active {
    color: #409eff;
    font-weight: 600;
}
.top-nav {
    display: flex;
    align-items: center;
    height: 40px;
    border-bottom: 1px solid #ebeef5;
}

.nav-items {
    display: flex;
}

.nav-item {
    padding: 0 15px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
}
.right-nav {
    margin-left: auto;
}
.search-icon {
    color: #409eff;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.search-icon i {
    margin-right: 5px;
}

.search-panel {
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: normal;
    .snbc-form {
        margin-top: 20px;
    }
    ::v-deep .el-form-item {
        display: inline-flex;
        width: 100%;
        margin-right: 0;
        margin-bottom: 8px;
        .el-form-item__label {
            flex-shrink: 0;
        }
        .el-form-item__content {
            flex: 1;
            padding-right: 15px;
        }

        .el-date-editor {
            width: 100%;
        }
    }
}

.button-group {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 10px;
}

.collapse-icon {
    position: absolute;
    right: 20px;
    bottom: 20px;
    cursor: pointer;
    color: #0c64eb;
}
.form-item-height {
    height: 42px;
}

.nav-select {
    margin-left: 5px;
    margin-right: 15px;
    width: 200px;
    height: 28px;

    ::v-deep .el-input__inner {
        border-radius: 4px;
        border-color: #dcdfe6;
        color: #606266;
        font-size: 13px;
        height: 28px;
        line-height: 28px;
        transition: all 0.2s;

        &:hover,
        &:focus {
            border-color: #409eff;
        }
    }

    &.el-select--small ::v-deep .el-input .el-input__inner {
        color: #409eff;
        font-weight: 600;
    }

    ::v-deep .el-input__suffix {
        right: 5px;
    }

    ::v-deep .el-select__caret {
        color: #909399;
        font-size: 12px;
    }
}
</style>
