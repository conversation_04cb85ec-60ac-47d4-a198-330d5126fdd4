<template>
    <div>
        <div class="query-container">
            <el-date-picker
                class="mr-10"
                v-model="date"
                placeholder="请选择日期"
                ref="dataPicker"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :clearable="false"
            >
            </el-date-picker>
            <el-button type="primary" @click="handleQuickAccess"
                >今天</el-button
            >
        </div>
        <div class="room-booking-table">
            <el-table
                :data="roomData"
                style="width: 100%"
                :header-cell-style="{
                    background: '#4A90E2',
                    color: '#fff',
                    textAlign: 'center'
                }"
                height="calc(100vh - 140px)"
                :cell-class-name="getCellClassName"
            >
                <!-- 会议室列 -->
                <el-table-column
                    prop="roomName"
                    label="会议室"
                    min-width="200"
                    :showOverflowTooltip="true"
                ></el-table-column>

                <!-- 时间段列组 - 每个小时一个组，包含00和30两个子列 -->
                <el-table-column
                    v-for="hour in hours"
                    :key="hour"
                    :label="hour.toString()"
                    align="center"
                >
                    <!-- 00分钟子列 -->
                    <el-table-column
                        :label="'00'"
                        min-width="35"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-popover
                                v-if="!isTimeAvailable(scope.row, `${hour}_00`)"
                                placement="top"
                                trigger="hover"
                                :open-delay="100"
                            >
                                <template slot="reference">
                                    <div
                                        :class="
                                            getTimeSlotClass(
                                                scope.row,
                                                `${hour}_00`
                                            )
                                        "
                                        class="time-cell"
                                    ></div>
                                </template>
                                <div class="meeting-popover">
                                    <div class="meeting-info-item">
                                        <span class="label">会议名称：</span>
                                        <span class="value">{{
                                            getMeetingDisplayName(
                                                scope.row,
                                                `${hour}_00`
                                            )
                                        }}</span>
                                    </div>
                                    <div class="meeting-info-item">
                                        <span class="label">预定时间：</span>
                                        <span class="value">{{
                                            getMeetingDisplayTime(
                                                scope.row,
                                                `${hour}_00`
                                            )
                                        }}</span>
                                    </div>
                                    <div class="meeting-info-item">
                                        <span class="label">预定人：</span>
                                        <span class="value">{{
                                            getMeetingDisplayCaller(
                                                scope.row,
                                                `${hour}_00`
                                            )
                                        }}</span>
                                    </div>
                                    <div
                                        class="meeting-actions"
                                        v-if="
                                            canUpdateMeeting(
                                                scope.row,
                                                `${hour}_00`
                                            )
                                        "
                                    >
                                        <el-button
                                            type="primary"
                                            size="mini"
                                            @click="
                                                handleEditMeeting(
                                                    scope.row,
                                                    `${hour}_00`
                                                )
                                            "
                                        >
                                            修改
                                        </el-button>
                                        <el-button
                                            type="danger"
                                            size="mini"
                                            @click="
                                                handleDeleteMeeting(
                                                    scope.row,
                                                    `${hour}_00`
                                                )
                                            "
                                        >
                                            删除
                                        </el-button>
                                    </div>
                                </div>
                            </el-popover>
                            <div
                                v-else
                                :class="
                                    getTimeSlotClass(scope.row, `${hour}_00`)
                                "
                                class="time-cell"
                                :data-room-id="scope.row.id"
                                :data-time-slot="`${hour}_00`"
                                @mousedown="
                                    handleMouseDown(
                                        $event,
                                        scope.row,
                                        `${hour}_00`
                                    )
                                "
                                @mouseenter="
                                    handleMouseEnter(
                                        $event,
                                        scope.row,
                                        `${hour}_00`
                                    )
                                "
                                @mouseup="handleMouseUp"
                            ></div>
                        </template>
                    </el-table-column>

                    <!-- 30分钟子列 -->
                    <el-table-column
                        :label="'30'"
                        min-width="35"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-popover
                                v-if="!isTimeAvailable(scope.row, `${hour}_30`)"
                                placement="top"
                                trigger="hover"
                                :open-delay="100"
                            >
                                <template slot="reference">
                                    <div
                                        :class="
                                            getTimeSlotClass(
                                                scope.row,
                                                `${hour}_30`
                                            )
                                        "
                                        class="time-cell"
                                    ></div>
                                </template>
                                <div class="meeting-popover">
                                    <div class="meeting-info-item">
                                        <span class="label">会议名称：</span>
                                        <span class="value">{{
                                            getMeetingDisplayName(
                                                scope.row,
                                                `${hour}_30`
                                            )
                                        }}</span>
                                    </div>
                                    <div class="meeting-info-item">
                                        <span class="label">预定时间：</span>
                                        <span class="value">{{
                                            getMeetingDisplayTime(
                                                scope.row,
                                                `${hour}_30`
                                            )
                                        }}</span>
                                    </div>
                                    <div class="meeting-info-item">
                                        <span class="label">预定人：</span>
                                        <span class="value">{{
                                            getMeetingDisplayCaller(
                                                scope.row,
                                                `${hour}_30`
                                            )
                                        }}</span>
                                    </div>
                                    <div
                                        class="meeting-actions"
                                        v-if="
                                            canUpdateMeeting(
                                                scope.row,
                                                `${hour}_30`
                                            )
                                        "
                                    >
                                        <el-button
                                            type="primary"
                                            size="mini"
                                            @click="
                                                handleEditMeeting(
                                                    scope.row,
                                                    `${hour}_30`
                                                )
                                            "
                                        >
                                            修改
                                        </el-button>
                                        <el-button
                                            type="danger"
                                            size="mini"
                                            @click="
                                                handleDeleteMeeting(
                                                    scope.row,
                                                    `${hour}_30`
                                                )
                                            "
                                        >
                                            删除
                                        </el-button>
                                    </div>
                                </div>
                            </el-popover>
                            <div
                                v-else
                                :class="
                                    getTimeSlotClass(scope.row, `${hour}_30`)
                                "
                                class="time-cell"
                                :data-room-id="scope.row.id"
                                :data-time-slot="`${hour}_30`"
                                @mousedown="
                                    handleMouseDown(
                                        $event,
                                        scope.row,
                                        `${hour}_30`
                                    )
                                "
                                @mouseenter="
                                    handleMouseEnter(
                                        $event,
                                        scope.row,
                                        `${hour}_30`
                                    )
                                "
                                @mouseup="handleMouseUp"
                            ></div>
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table>
        </div>
        <BookRoomDialog
            :visible.sync="bookRoomDialogVisible"
            :id="currentMeetingId"
            :meetingInfo="currnetMeetingData"
            :meetingRooms="currnetMeetingRooms"
            :allMeetingInfo="meetingsData"
            @success="update"
        ></BookRoomDialog>
    </div>
</template>

<script>
import moment from 'moment';
import BookRoomDialog from './BookRoomDialog';
import { getUserAccount } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'MeetingQuality',
    components: { BookRoomDialog },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        meetingId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            date: moment().format('YYYY-MM-DD'),
            // 小时数组
            hours: [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
            timeRange: '',
            // 会议室数据
            roomData: [],
            // 会议数据
            meetingsData: [],
            // 拖拽选择相关数据
            isSelecting: false,
            selectedCells: [],
            startCell: null,
            bookRoomDialogVisible: false,
            // 当前选中的会议id
            currentMeetingId: '',
            // 选中的时间段信息
            currnetMeetingData: {}
        };
    },
    computed: {
        // 当前会议室信息
        currnetMeetingRooms() {
            return this.roomData.map((i) => ({
                value: i.id.toString(),
                label: i.roomName
            }));
        }
    },
    watch: {
        async date(newVal) {
            if (newVal) {
                this.update();
            }
        }
    },
    async created() {
        this.initRoomAvailability();
        await Promise.all([this.getMeetingInfo(), this.getRoomData()]);
        this.updateRoomAvailability();
    },
    methods: {
        /**
         * 解析地址字符串为数组
         * @param {String|Array} address - 地址字符串或数组
         * @returns {Array} 地址数组
         */
        parseAddresses(address) {
            if (!address) return [];
            if (Array.isArray(address)) return address;
            return address.split(',').map((addr) => addr.trim());
        },

        async getRoomData() {
            const api = this.$service.feature.meetingRoom.getRoomList;
            const params = {};
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.roomData = res.body.map((i) => ({
                    ...i,
                    roomName: i.name
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        async getMeetingInfo() {
            const api = this.$service.feature.meetingRoom.getMeetingInfo;
            const params = {
                beginDate: this.date,
                endDate: this.date
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.meetingsData = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
        },

        // 初始化数据
        async update() {
            this.initRoomAvailability();
            await this.getMeetingInfo();
            this.updateRoomAvailability();
        },
        // 处理鼠标按下事件
        handleMouseDown(event, room, timeSlot) {
            // 防止默认的拖拽行为
            event.preventDefault();

            // 只有可用的格子才能开始选择
            if (!this.isTimeAvailable(room, timeSlot)) {
                return;
            }

            this.isSelecting = true;
            this.startCell = { room, timeSlot };
            this.selectedCells = [];

            // 添加起始格子到选择列表
            this.addCellToSelection(room, timeSlot);
        },

        // 处理鼠标进入事件
        handleMouseEnter(event, room, timeSlot) {
            if (!this.isSelecting) return;

            // 只有可用的格子才能被选择
            if (!this.isTimeAvailable(room, timeSlot)) {
                return;
            }

            // 限制只能在同一行（同一会议室）内拖拽
            if (this.startCell && this.startCell.room.id !== room.id) {
                return;
            }

            // 添加格子到选择列表
            this.addCellToSelection(room, timeSlot);
        },

        // 处理鼠标抬起事件
        handleMouseUp() {
            // TODO: 判断拖拽的时间范围是否包含不可用时间
            this.isSelecting = false;
            this.createMeeting();
        },

        // 添加格子到选择列表
        addCellToSelection(room, timeSlot) {
            // 如果没有起始点，直接添加
            if (!this.startCell) {
                const cellKey = `${room.id}_${timeSlot}`;
                this.selectedCells = [
                    {
                        room,
                        timeSlot,
                        cellKey
                    }
                ];
                return;
            }

            // 计算从起始点到当前点的连续区域
            const continuousSlots = this.getContinuousTimeSlots(
                this.startCell.timeSlot,
                timeSlot
            );

            // 清空当前选择，重新添加连续区域
            this.selectedCells = [];

            // 添加所有连续的可用时间段
            continuousSlots.forEach((slot) => {
                if (this.isTimeAvailable(room, slot)) {
                    const cellKey = `${room.id}_${slot}`;
                    this.selectedCells.push({
                        room,
                        timeSlot: slot,
                        cellKey
                    });
                }
            });
        },

        // 获取两个时间段之间的连续时间段
        getContinuousTimeSlots(startSlot, endSlot) {
            const [startHour, startMinute] = startSlot.split('_').map(Number);
            const [endHour, endMinute] = endSlot.split('_').map(Number);

            const startMinutes = startHour * 60 + startMinute;
            const endMinutes = endHour * 60 + endMinute;

            // 确保起始时间小于结束时间
            const minMinutes = Math.min(startMinutes, endMinutes);
            const maxMinutes = Math.max(startMinutes, endMinutes);

            const slots = [];

            // 生成连续的30分钟时间段
            for (
                let minutes = minMinutes;
                minutes <= maxMinutes;
                minutes += 30
            ) {
                const hour = Math.floor(minutes / 60);
                const minute = minutes % 60;

                // 只添加在工作时间范围内的时间段
                if (this.hours.includes(hour)) {
                    slots.push(`${hour}_${minute.toString().padStart(2, '0')}`);
                }
            }

            return slots;
        },

        // 检查格子是否被选中
        isCellSelected(room, timeSlot) {
            return this.selectedCells.some(
                (cell) => cell.room.id === room.id && cell.timeSlot === timeSlot
            );
        },

        // 清除选择
        clearSelection() {
            this.selectedCells = [];
            this.isSelecting = false;
            this.startCell = null;
        },

        // 创建会议
        createMeeting() {
            if (this.selectedCells.length === 0) {
                this.$message.warning('请先选择时间段');
                return;
            }

            // 检查是否选择了同一个会议室的连续时间段
            const roomGroups = this.groupCellsByRoom();

            if (Object.keys(roomGroups).length > 1) {
                this.$message.warning('请选择同一个会议室的时间段');
                return;
            }

            const address = Object.keys(roomGroups)[0];
            const timeSlots = roomGroups[address];

            // 检查时间段是否连续（可以判断出来中间有不可用时间）
            if (!this.areTimeSlotsConsecutive(timeSlots)) {
                this.$message.warning('该会议室已有会议安排，不能申请会议');
                return;
            }

            // 计算开始和结束时间
            const { startTime, endTime } = this.calculateTimeRange(timeSlots);

            // 创建会议对象
            this.currnetMeetingData = {
                address: [address],
                date: this.date,
                timeRange: [startTime, endTime],
                meetingName: ''
            };

            // 清空会议ID，表示这是新建会议
            this.currentMeetingId = '';

            // 打开弹窗
            this.bookRoomDialogVisible = true;

            // 清除选择状态
            this.clearSelection();
        },

        // 按会议室分组格子
        groupCellsByRoom() {
            const groups = {};
            this.selectedCells.forEach((cell) => {
                const address = cell.room.id.toString();
                if (!groups[address]) {
                    groups[address] = [];
                }
                groups[address].push(cell.timeSlot);
            });
            return groups;
        },

        // 检查时间段是否连续
        areTimeSlotsConsecutive(timeSlots) {
            if (timeSlots.length <= 1) return true;

            // 将时间段转换为分钟数进行排序
            const sortedSlots = timeSlots
                .map((slot) => {
                    const [hour, minute] = slot.split('_');
                    return {
                        slot,
                        minutes: parseInt(hour) * 60 + parseInt(minute)
                    };
                })
                .sort((a, b) => a.minutes - b.minutes);

            // 检查是否连续（每个时间段间隔30分钟）
            for (let i = 1; i < sortedSlots.length; i++) {
                if (
                    sortedSlots[i].minutes - sortedSlots[i - 1].minutes !==
                    30
                ) {
                    return false;
                }
            }
            return true;
        },

        // 计算时间范围
        calculateTimeRange(timeSlots) {
            const slots = timeSlots
                .map((slot) => {
                    const [hour, minute] = slot.split('_');
                    return {
                        slot,
                        minutes: parseInt(hour) * 60 + parseInt(minute)
                    };
                })
                .sort((a, b) => a.minutes - b.minutes);

            const startMinutes = slots[0].minutes;
            const endMinutes = slots[slots.length - 1].minutes + 30; // 加30分钟到最后一个时间段

            const startHour = Math.floor(startMinutes / 60);
            const startMin = startMinutes % 60;
            const endHour = Math.floor(endMinutes / 60);
            const endMin = endMinutes % 60;

            return {
                startTime: `${startHour.toString().padStart(2, '0')}:${startMin
                    .toString()
                    .padStart(2, '0')}`,
                endTime: `${endHour.toString().padStart(2, '0')}:${endMin
                    .toString()
                    .padStart(2, '0')}`
            };
        },

        // 初始化会议室可用性
        initRoomAvailability() {
            this.roomData.forEach((room) => {
                // 确保 availability 对象存在
                if (!room.availability) {
                    this.$set(room, 'availability', {});
                }
                // 初始化所有时间段为可用
                this.hours.forEach((hour) => {
                    this.$set(room.availability, `${hour}_00`, true);
                    this.$set(room.availability, `${hour}_30`, true);
                });
            });
        },

        // 更新会议室可用性
        updateRoomAvailability() {
            // 重置所有时间段为可用
            this.initRoomAvailability();

            // 清空所有会议室的会议数据，避免重复添加
            this.roomData.forEach((room) => {
                this.$set(room, 'meetings', []);
            });

            // 根据会议数据标记占用的时间段
            this.meetingsData.forEach((meeting) => {
                if (
                    meeting.beginDate === this.date &&
                    meeting.endDate === this.date
                ) {
                    // 处理会议室地址可能包含多个地址的情况（用英文逗号分隔）
                    const meetingAddresses = this.parseAddresses(
                        meeting.address
                    );

                    meetingAddresses.forEach((roomId) => {
                        const room = this.roomData.find(
                            (r) => r.id.toString() === roomId.trim()
                        );

                        if (room) {
                            const occupiedSlots = this.getOccupiedTimeSlots(
                                meeting.beginTime,
                                meeting.endTime
                            );

                            occupiedSlots.forEach((slot) => {
                                this.$set(room.availability, slot, false);
                            });

                            // 存储会议信息到房间数据中
                            room.meetings.push(meeting);
                        }
                    });
                }
            });
        },

        // 根据开始和结束时间获取占用的时间段
        getOccupiedTimeSlots(oriBeginTime, oriEndTime) {
            const slots = [];

            const startTime = moment(oriBeginTime, 'HH:mm');
            const endTime = moment(oriEndTime, 'HH:mm');

            const current = startTime.clone();
            const minutes = current.minute();

            if (minutes > 0) {
                if (minutes >= 30) {
                    current.minute(30).second(0);
                } else {
                    current.minute(0).second(0);
                }
            } else {
                current.second(0);
            }

            while (current.isBefore(endTime)) {
                const hour = current.hour();
                const minute = current.minute();

                if (this.hours.includes(hour)) {
                    slots.push(`${hour}_${minute.toString().padStart(2, '0')}`);
                }

                current.add(30, 'minutes');
            }

            return slots;
        },
        // 检查时间段是否可用
        isTimeAvailable(room, timeSlot) {
            // 确保 availability 对象存在，如果不存在则认为时间可用
            if (!room.availability) {
                return true;
            }
            return room.availability[timeSlot] !== false;
        },

        // 获取时间段的CSS类
        getTimeSlotClass(room, timeSlot) {
            const classes = [];
            if (!this.isTimeAvailable(room, timeSlot)) {
                classes.push('occupied');
            } else {
                classes.push('available');
                // 添加选中状态的类
                if (this.isCellSelected(room, timeSlot)) {
                    classes.push('selected');
                }
            }

            return classes;
        },

        // 快速跳转
        handleQuickAccess() {
            this.date = moment().format('YYYY-MM-DD');
        },

        // 获取单元格样式
        getCellClassName({ columnIndex }) {
            // 第一列是会议室列，从第二列开始都是时间列
            if (columnIndex >= 1) {
                return 'row-time-cell';
            }
            return '';
        },

        // 根据时间段获取对应的会议信息
        getMeetingByTimeSlot(room, timeSlot) {
            if (!room.meetings || room.meetings.length === 0) {
                return null;
            }

            // 查找包含该时间段的会议
            return room.meetings.find((meeting) => {
                const { beginTime, endTime } = meeting;

                // 获取该会议占用的所有时间段
                const occupiedSlots = this.getOccupiedTimeSlots(
                    beginTime,
                    endTime
                );

                // 检查当前时间段是否在占用的时间段列表中
                return occupiedSlots.includes(timeSlot);
            });
        },

        // 获取会议名称显示
        getMeetingDisplayName(room, timeSlot) {
            const meeting = this.getMeetingByTimeSlot(room, timeSlot);
            return meeting && meeting.name ? meeting.name : '未知会议';
        },

        // 获取会议时间显示
        getMeetingDisplayTime(room, timeSlot) {
            const meeting = this.getMeetingByTimeSlot(room, timeSlot);
            if (!meeting) {
                return '00:00 - 00:00';
            }
            const beginTime = meeting.beginTime || '00:00';
            const endTime = meeting.endTime || '00:00';
            return `${beginTime} - ${endTime}`;
        },

        // 获取会议预定人显示
        getMeetingDisplayCaller(room, timeSlot) {
            const meeting = this.getMeetingByTimeSlot(room, timeSlot);
            return meeting && meeting.callerLastName
                ? meeting.callerLastName
                : '未知';
        },

        // 处理修改会议
        handleEditMeeting(room, timeSlot) {
            const meeting = this.getMeetingByTimeSlot(room, timeSlot);
            if (!meeting) {
                this.$message.warning('未找到会议信息');
                return;
            }

            // 设置当前会议ID，用于区分是编辑还是新建
            this.currentMeetingId = meeting.id;

            // 构造会议数据，格式与createMeeting保持一致
            this.currnetMeetingData = {
                address: meeting.address, // 会议室ID
                date: meeting.beginDate, // 会议日期
                timeRange: [meeting.beginTime, meeting.endTime], // 时间范围
                meetingName: meeting.name, // 会议名称
                id: meeting.id // 会议ID，用于编辑时识别
            };

            // 打开弹窗
            this.bookRoomDialogVisible = true;
        },

        // 检查是否可以删除会议（只有会议未开始时才能删除）
        canUpdateMeeting(room, timeSlot) {
            const meeting = this.getMeetingByTimeSlot(room, timeSlot);
            if (!meeting) {
                return false;
            }
            // 只有本人才能编辑
            const hasUpdatePermission =
                getUserAccount(this) === meeting.createrLoginId;

            return hasUpdatePermission && this.isMeetingNotStarted(meeting);
        },

        // 判断会议是否未开始
        isMeetingNotStarted(meeting) {
            // 获取当前时间
            const now = moment();

            // 构建会议真实开始时间（使用会议的原始beginTime，不是格子调整后的时间）
            const meetingStartDateTime = moment(
                `${meeting.beginDate} ${meeting.beginTime}`,
                'YYYY-MM-DD HH:mm'
            );

            // 只有当前时间在会议开始时间之前才能删除
            return now.isBefore(meetingStartDateTime);
        },

        // 处理删除会议
        async handleDeleteMeeting(room, timeSlot) {
            const meeting = this.getMeetingByTimeSlot(room, timeSlot);

            if (!meeting) {
                this.$message.warning('未找到会议信息');
                return;
            }
            try {
                await this.$confirm(
                    `确定要删除会议"${meeting.name}"吗？`,
                    '确认删除',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                const api = this.$service.feature.meetingRoom.cancelBook;
                const params = {
                    meetingId: meeting.id
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('删除成功');
                this.update();
            } catch (error) {
                console.error('Error:', error);
            }
        },

        // 处理预订成功事件
        handleBookingSuccess() {
            // 重新加载会议数据以显示新预订的会议
            this.getMeetingInfo();
            // 清除选择状态
            this.clearSelection();
        }
    }
};
</script>

<style lang="scss" scoped>
.mr-10 {
    margin-right: 10px;
}
.ml-10 {
    margin-left: 10px;
}
.query-container {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.room-booking-table {
    .time-cell {
        width: 100%;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        user-select: none;

        &.occupied {
            cursor: no-drop;
            background-color: #ff5252;
        }

        &.available {
            background-color: #fff;
            &:hover {
                background-color: #ededed;
                border-color: #b3d8ff;
            }
        }

        &.selected {
            background-color: #e1ecff;
            border-color: #b3d8ff;
        }
    }
}
::v-deep .el-table__cell.row-time-cell {
    padding: 0 !important;
    height: auto;
    .cell {
        height: 100%;
        padding: 0;
    }
}

.meeting-popover {
    .meeting-info-item {
        margin-bottom: 8px;
        display: flex;
        align-items: center;

        &:last-child {
            margin-bottom: 0;
        }

        .label {
            font-weight: 500;
            color: #606266;
            min-width: 70px;
            flex-shrink: 0;
        }

        .value {
            color: #303133;
            flex: 1;
        }
    }

    .meeting-actions {
        margin-top: 12px;
        padding-top: 8px;
        border-top: 1px solid #ebeef5;
        display: flex;
        gap: 8px;
        justify-content: center;

        .el-button {
            padding: 5px 12px;
            font-size: 12px;
        }
    }
}
</style>
