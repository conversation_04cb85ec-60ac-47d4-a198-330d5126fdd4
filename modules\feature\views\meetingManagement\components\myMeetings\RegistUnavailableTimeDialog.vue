<template>
    <div>
        <el-dialog
            :visible.sync="dialogVisible"
            width="1200px"
            title="登记不可用时间"
            @close="closeDialog"
        >
            <el-tabs v-model="activeName">
                <el-tab-pane label="登记" name="regist">
                    <div class="time-regist-content">
                        <div class="add-time-section">
                            <el-button
                                type="primary"
                                size="mini"
                                @click="addTimeSlot"
                                class="add-btn"
                            >
                                新增时间段
                            </el-button>
                        </div>
                        <div
                            v-for="(item, index) in timeSlots"
                            :key="index"
                            class="time-slot-row"
                        >
                            <div class="row-content">
                                <span class="label">日期:</span>
                                <el-date-picker
                                    v-model="item.dateRange"
                                    @change="
                                        (value) =>
                                            handleDateChange(value, index)
                                    "
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="yyyy/MM/dd"
                                    value-format="yyyy-MM-dd"
                                    size="mini"
                                    class="date-picker"
                                    clearable
                                />

                                <span class="label">时间:</span>
                                <el-checkbox
                                    v-model="item.allDay"
                                    @change="handleAllDayChange(item)"
                                    :disabled="item.disableDateCheckbox"
                                    >全天</el-checkbox
                                >
                                <el-time-picker
                                    class="time-picker"
                                    size="mini"
                                    is-range
                                    v-model="item.timeRange"
                                    range-separator="至"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    placeholder="选择时间范围"
                                    format="HH:mm"
                                    value-format="HH:mm"
                                    :disabled="item.allDay"
                                    clearable
                                />

                                <span class="label">事由:</span>
                                <el-input
                                    v-model="item.reason"
                                    placeholder="请输入事由"
                                    size="mini"
                                    class="reason-input"
                                    maxlength="100"
                                />

                                <el-button
                                    v-if="timeSlots.length > 1"
                                    type="text"
                                    icon="el-icon-delete"
                                    @click="removeTimeSlot(index)"
                                    class="delete-btn"
                                />
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="历史记录" name="history">
                    <div class="history-content">
                        <el-table
                            :data="historyList"
                            border
                            style="width: 100%"
                            class="history-table"
                        >
                            <el-table-column
                                prop="fullTime"
                                label="时间"
                                width="320"
                                align="center"
                            />
                            <el-table-column
                                prop="reason"
                                label="事由"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    {{
                                        scope.row.disableFlag
                                            ? scope.row.reason
                                            : '请假（OA）'
                                    }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="操作"
                                width="120"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <!-- disableFlag为true按钮可用（为不可用时间），false代表按钮不可用（为请假）-->
                                    <el-button
                                        v-if="scope.row.disableFlag"
                                        type="text"
                                        size="small"
                                        @click="editHistoryItem(scope.row)"
                                    >
                                        修改
                                    </el-button>
                                    <el-button
                                        v-if="scope.row.disableFlag"
                                        type="text"
                                        size="small"
                                        style="color: #f56c6c"
                                        @click="deleteHistoryItem(scope.row)"
                                    >
                                        删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <pagination
                            class="history-content-pagination"
                            :total="total"
                            :page.sync="page"
                            :limit.sync="limit"
                            @pagination="getHistoryList"
                        />
                    </div>
                </el-tab-pane>
            </el-tabs>
            <div slot="footer" v-if="activeName === 'regist'">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">确 定</el-button>
            </div>
        </el-dialog>
        <!-- 编辑历史记录弹窗 -->
        <el-dialog
            :visible.sync="editDialogVisible"
            width="600px"
            title="编辑不可用时间"
            @close="closeEditDialog"
        >
            <el-form
                ref="editForm"
                :model="editForm"
                :rules="editRules"
                label-width="80px"
            >
                <el-form-item label="日期" prop="dateRange">
                    <el-date-picker
                        v-model="editForm.dateRange"
                        @change="handleEditDateChange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="yyyy/MM/dd"
                        value-format="yyyy-MM-dd"
                        style="width: 100%"
                        :clearable="false"
                    />
                </el-form-item>
                <el-form-item label="时间" prop="timeRange">
                    <div style="display: flex; align-items: center; gap: 10px">
                        <el-checkbox
                            v-model="editForm.allDay"
                            @change="handleEditAllDayChange"
                            :disabled="editForm.disableDateCheckbox"
                        >
                            全天
                        </el-checkbox>
                        <el-time-picker
                            v-model="editForm.timeRange"
                            is-range
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            format="HH:mm"
                            value-format="HH:mm"
                            :disabled="editForm.allDay"
                            style="flex: 1"
                            :clearable="false"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="事由" prop="reason">
                    <el-input
                        v-model="editForm.reason"
                        placeholder="请输入事由"
                        maxlength="100"
                        clearable
                        show-word-limit
                    />
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="closeEditDialog">取 消</el-button>
                <el-button type="primary" @click="saveEdit">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment';
import { getUserAccount } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'RegistUnavailableTimeDialog',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            activeName: 'regist',
            timeSlots: [
                {
                    dateRange: null,
                    timeRange: ['', ''],
                    allDay: false,
                    reason: '',
                    // 禁用日期勾选
                    disableDateCheckbox: false
                }
            ],
            // 历史记录相关数据
            historyList: [],
            total: 0,
            page: 1,
            limit: 10,
            // 编辑弹窗相关数据
            editDialogVisible: false,
            editForm: {
                id: null,
                dateRange: null,
                timeRange: null,
                allDay: false,
                reason: '',
                disableDateCheckbox: false
            },
            editRules: {
                dateRange: [
                    { required: true, message: '请选择日期', trigger: 'change' }
                ],
                timeRange: [
                    { required: true, message: '请选择时间', trigger: 'change' }
                ]
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.initializeData();
            } else {
                this.resetForm();
            }
        },
        activeName(newVal) {
            if (newVal === 'history') {
                this.getHistoryList();
            }
        }
    },
    methods: {
        /**
         * 初始化数据
         */
        initializeData() {
            this.timeSlots = Array.from({ length: 2 }).map(() => ({
                dateRange: null,
                timeRange: null,
                allDay: false,
                reason: ''
            }));
        },

        /**
         * 处理全天选择变化
         * @param {Object} item - 时间段项目
         */
        handleAllDayChange(item) {
            if (item.allDay) {
                item.timeRange = ['00:00', '23:59'];
            } else {
                item.timeRange = ['', ''];
            }
        },

        /**
         * 新增时间段
         */
        addTimeSlot() {
            this.timeSlots.push({
                startDate: '',
                endDate: '',
                timeRange: null,
                allDay: false,
                reason: '',
                disableDateCheckbox: false
            });
        },

        /**
         * 删除时间段
         * @param {number} index - 要删除的时间段索引
         */
        removeTimeSlot(index) {
            if (this.timeSlots.length > 1) {
                this.timeSlots.splice(index, 1);
            }
        },

        /**
         * 重置表单
         */
        resetForm() {
            this.activeName = 'regist';
            this.timeSlots = [
                {
                    startDate: '',
                    endDate: '',
                    timeRange: ['', ''],
                    allDay: false,
                    reason: '',
                    disableDateCheckbox: false
                }
            ];
        },

        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.resetForm();
            this.dialogVisible = false;
        },

        /**
         * 保存数据
         */
        async save() {
            // 验证数据
            const isValid = this.validateTimeSlots();
            if (!isValid) {
                return;
            }

            // 处理保存逻辑
            const params = this.timeSlots.map((slot) => ({
                startDate: slot.dateRange[0],
                endDate: slot.dateRange[1],
                startTime: slot.timeRange[0] || '',
                endTime: slot.timeRange[1] || '',
                allDayFlag: slot.allDay ? '全天' : '',
                reason: slot.reason,
                account: getUserAccount(this)
            }));

            const api = this.$service.feature.unavailableTime.add;
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('保存成功');
                this.closeDialog();
            } catch (error) {
                console.error('Error:', error);
            }
        },

        /**
         * 验证时间段数据
         * @returns {boolean} 验证结果
         */
        validateTimeSlots() {
            for (let i = 0; i < this.timeSlots.length; i++) {
                const slot = this.timeSlots[i];

                if (
                    !slot.dateRange ||
                    !slot.dateRange[0] ||
                    !slot.dateRange[1] ||
                    !slot.timeRange ||
                    !slot.timeRange[0] ||
                    !slot.timeRange[1]
                ) {
                    this.$message.warning(`请选择日期与时间`);
                    return false;
                }
            }

            return true;
        },
        /**
         * 处理日期变化
         * @param {Array} value - 日期范围数组 [startDate, endDate]
         * @param {number} index - 当前时间段的索引
         */
        handleDateChange(value, index) {
            if (!value || !Array.isArray(value) || value.length !== 2) {
                return;
            }

            const [startDate, endDate] = value;
            const currentSlot = this.timeSlots[index];

            if (currentSlot && startDate && endDate) {
                const start = moment(startDate);
                const end = moment(endDate);

                // 如果结束日期大于开始日期（跨天），自动勾选全天，不允许选择时间
                if (end.isAfter(start, 'day')) {
                    currentSlot.allDay = true;
                    currentSlot.disableDateCheckbox = true;
                    currentSlot.timeRange = ['00:00', '23:59'];
                } else {
                    currentSlot.disableDateCheckbox = false;
                }
            }
        },

        /**
         * 获取历史记录列表
         */
        async getHistoryList() {
            try {
                const params = {
                    currentPage: this.page,
                    pageSize: this.limit,
                    myAccount: getUserAccount(this)
                };

                // 这里需要替换为实际的API
                const api = this.$service.feature.unavailableTime.getInfo;
                const res = await api(params);

                if (res.head.code === '000000') {
                    console.log(res, 'res');

                    this.historyList = res.body.list.map((item) => ({
                        ...item,
                        fullTime: this.formatFullTime(item)
                    }));
                    this.total = res.body.total;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error('获取历史记录失败:', error);
                this.$message.error('获取历史记录失败');
            }
        },

        /**
         * 格式化日期范围显示
         */
        formatFullTime(item) {
            const { startDate, endDate, startTime, endTime } = item;
            return `${startDate} ${startTime} - ${endDate} ${endTime}`;
        },

        /**
         * 编辑历史记录项
         */
        editHistoryItem(row) {
            this.editForm = {
                id: row.id,
                dateRange: [row.startDate, row.endDate],
                timeRange:
                    row.allDayFlag === '全天'
                        ? ['00:00', '23:59']
                        : [row.startTime, row.endTime],
                allDay: row.allDayFlag === '全天',
                reason: row.reason,
                disableDateCheckbox: row.startDate !== row.endDate
            };
            this.editDialogVisible = true;
        },

        /**
         * 删除历史记录项
         */
        async deleteHistoryItem(row) {
            try {
                await this.$confirm('确定要删除这条记录吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                // 这里需要替换为实际的删除API
                const api = this.$service.feature.unavailableTime.delete;
                const res = await api({ unavailableTimeId: row.id });

                if (res.head.code === '000000') {
                    this.$message.success('删除成功');
                    this.getHistoryList();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('删除失败:', error);
                    this.$message.error('删除失败');
                }
            }
        },

        /**
         * 处理编辑表单的日期变化
         */
        handleEditDateChange(value) {
            if (!value || !Array.isArray(value) || value.length !== 2) {
                return;
            }

            const [startDate, endDate] = value;
            if (startDate && endDate) {
                const start = moment(startDate);
                const end = moment(endDate);

                // 如果结束日期大于开始日期（跨天），自动勾选全天
                if (end.isAfter(start, 'day')) {
                    this.editForm.allDay = true;
                    this.editForm.disableDateCheckbox = true;
                    this.editForm.timeRange = ['00:00', '23:59'];
                } else {
                    this.editForm.disableDateCheckbox = false;
                }
            }
        },

        /**
         * 处理编辑表单的全天选择变化
         */
        handleEditAllDayChange() {
            if (this.editForm.allDay) {
                this.editForm.timeRange = ['00:00', '23:59'];
            } else {
                this.editForm.timeRange = ['', ''];
            }
        },

        /**
         * 关闭编辑弹窗
         */
        closeEditDialog() {
            this.editDialogVisible = false;
            this.$refs.editForm.resetFields();
            this.editForm = {
                id: null,
                dateRange: null,
                timeRange: null,
                allDay: false,
                reason: '',
                disableDateCheckbox: false
            };
        },

        /**
         * 保存编辑
         */
        async saveEdit() {
            try {
                const validResult = await this.$refs.editForm.validate();
                console.log(validResult, 'res');

                const params = {
                    id: this.editForm.id,
                    startDate: this.editForm.dateRange[0],
                    endDate: this.editForm.dateRange[1],
                    startTime: this.editForm.timeRange[0] || '',
                    endTime: this.editForm.timeRange[1] || '',
                    allDayFlag: this.editForm.allDay ? '全天' : '',
                    reason: this.editForm.reason,
                    account: getUserAccount(this)
                };

                // 这里需要替换为实际的更新API
                const api = this.$service.feature.unavailableTime.edit;
                const res = await api(params);

                if (res.head.code === '000000') {
                    this.$message.success('修改成功');
                    this.closeEditDialog();
                    this.getHistoryList();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                if (error !== false) {
                    console.error('保存失败:', error);
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.add-btn {
    margin-bottom: 5px;
}
.time-slot-row {
    margin-bottom: 15px;

    .row-content {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 5px 5px;
        flex-wrap: wrap;
    }
}

.label {
    font-weight: 500;
    color: #606266;
    white-space: nowrap;
    margin-left: 15px;

    &:first-child {
        margin-left: 0;
    }
}

.date-picker {
    width: 250px;
}

.time-picker {
    width: 200px;
}

.separator {
    color: #909399;
    margin: 0 2px;
}

.el-checkbox {
    margin-right: 10px;
}

.reason-input {
    flex: 1;
    min-width: 150px;
}

.delete-btn {
    color: #f56c6c;
    margin-left: 10px;

    &:hover {
        color: #f78989;
    }
}

.add-time-section {
    text-align: right;
}

.history-content .history-content-pagination {
    padding: 0;
}
// 这里公共样式影响了el-inpu的高度，修复过来
::v-deep .el-dialog .el-dialog__body .el-input .el-input__inner {
    height: 28px !important;
    line-height: 28px !important;
}
</style>
