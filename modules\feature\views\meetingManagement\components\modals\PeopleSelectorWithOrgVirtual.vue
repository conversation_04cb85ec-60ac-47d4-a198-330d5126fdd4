<template>
    <el-dialog
        title="选择人员"
        :visible.sync="dialogVisible"
        width="1000px"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <div class="people-tree-container">
            <!-- 全部人员根节点 -->
            <div class="tree-node root-node">
                <div class="node-content">
                    <el-checkbox
                        :indeterminate="isIndeterminate"
                        :value="checkAll"
                        @change="handleCheckAllChange"
                    >
                        全部人员
                    </el-checkbox>
                </div>

                <!-- 部门列表 -->
                <div class="children-container">
                    <div
                        v-for="org in orgList"
                        :key="org.id"
                        class="tree-node org-node"
                    >
                        <div class="node-content">
                            <el-checkbox
                                :indeterminate="getOrgIndeterminate(org)"
                                :value="isOrgSelected(org)"
                                @change="toggleOrgSelection(org)"
                            >
                                {{ org.label }}
                            </el-checkbox>
                        </div>

                        <!-- 虚拟滚动人员网格 -->
                        <div class="people-grid-container">
                            <virtual-list
                                :size="32"
                                :remain="20"
                                :bench="10"
                                :start="0"
                                :offset="0"
                                :item="PersonItem"
                                :itemcount="getOrgPeople(org.id).length"
                                :itemprops="getItemProps(org.id)"
                                class="virtual-people-list"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">
                确 定 ({{ selectedPeople.length }})
            </el-button>
        </div>
    </el-dialog>
</template>

<script>
// 人员选择组件虚拟滚动版本
import VirtualList from 'vue-virtual-scroll-list';

// 人员项组件
const PersonItem = {
    props: ['index', 'source'],
    template: `
        <div class="person-item" @click="handleClick">
            <el-checkbox
                :value="source.isSelected"
                @change="handleClick"
                class="person-checkbox"
            />
            <span class="person-name">{{ source.person.name }}</span>
        </div>
    `,
    methods: {
        handleClick() {
            this.$emit('toggle-person', this.source.person);
        }
    }
};

export default {
    name: 'PeopleSelectorWithOrgVirtual',
    components: {
        VirtualList
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        value: {
            type: Array,
            default: () => []
        },
        multiple: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            orgList: [],
            allPeopleList: [],
            selectedPeople: [],
            selectedPeopleSet: new Set(),
            checkAll: false,
            isIndeterminate: false,
            PersonItem,
            // 缓存
            orgPeopleCache: {},
            orgSelectedCache: {},
            orgIndeterminateCache: {}
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        },
        orgPeopleMap() {
            const map = {};
            this.allPeopleList.forEach((person) => {
                if (!map[person.orgId]) {
                    map[person.orgId] = [];
                }
                map[person.orgId].push(person);
            });
            return map;
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                this.initData();
            }
        },
        value: {
            immediate: true,
            handler(newVal) {
                this.selectedPeople = [...(newVal || [])];
                this.updateSelectedPeopleSet();
            }
        },
        selectedPeople() {
            this.updateCheckAllStatus();
            this.clearCache();
        }
    },
    methods: {
        async initData() {
            await this.getOrgData();
            await this.getAllPeopleList();
            this.updateSelectedPeopleSet();
        },

        async getOrgData() {
            try {
                this.orgList = [
                    { id: '2', label: '结构开发部' },
                    { id: '3', label: '硬件开发部' },
                    { id: '4', label: '软件开发部' }
                ];
            } catch (error) {
                console.error('获取组织架构失败:', error);
                this.$message.error('获取组织架构失败');
            }
        },

        async getAllPeopleList() {
            try {
                const names = [
                    '张三丰',
                    '张三',
                    '张二丰',
                    '张三十',
                    '张二十',
                    '李四',
                    '王五',
                    '赵六',
                    '钱七',
                    '孙八'
                ];
                const allPeople = [];

                this.orgList.forEach((org) => {
                    // 每个部门生成200个人员用于测试虚拟滚动
                    for (let i = 0; i < 200; i++) {
                        allPeople.push({
                            id: `${org.id}_${i + 1}`,
                            name: `${names[i % names.length]}${i + 1}`,
                            orgId: org.id
                        });
                    }
                });

                this.allPeopleList = allPeople;
            } catch (error) {
                console.error('获取人员列表失败:', error);
                this.$message.error('获取人员列表失败');
            }
        },

        // 获取虚拟列表项属性
        getItemProps(orgId) {
            const orgPeople = this.getOrgPeople(orgId);
            return (index) => {
                const person = orgPeople[index];
                return {
                    person,
                    isSelected: this.isPersonSelected(person)
                };
            };
        },

        updateSelectedPeopleSet() {
            this.selectedPeopleSet.clear();
            this.selectedPeople.forEach((person) => {
                this.selectedPeopleSet.add(person.id);
            });
        },

        clearCache() {
            this.orgSelectedCache = {};
            this.orgIndeterminateCache = {};
        },

        updateCheckAllStatus() {
            const checkedCount = this.selectedPeople.length;
            this.checkAll = checkedCount === this.allPeopleList.length;
            this.isIndeterminate =
                checkedCount > 0 && checkedCount < this.allPeopleList.length;
        },

        handleCheckAllChange(val) {
            if (val) {
                this.selectedPeople = [...this.allPeopleList];
            } else {
                this.selectedPeople = [];
            }
            this.updateSelectedPeopleSet();
        },

        isPersonSelected(person) {
            return this.selectedPeopleSet.has(person.id);
        },

        togglePersonSelection(person) {
            if (this.isPersonSelected(person)) {
                this.removeSelectedPerson(person);
            } else if (!this.multiple) {
                this.selectedPeople = [person];
                this.updateSelectedPeopleSet();
            } else {
                this.selectedPeople.push(person);
                this.selectedPeopleSet.add(person.id);
            }
        },

        removeSelectedPerson(person) {
            const index = this.selectedPeople.findIndex(
                (p) => p.id === person.id
            );
            if (index > -1) {
                this.selectedPeople.splice(index, 1);
                this.selectedPeopleSet.delete(person.id);
            }
        },

        getOrgPeople(orgId) {
            return this.orgPeopleMap[orgId] || [];
        },

        isOrgSelected(org) {
            if (this.orgSelectedCache[org.id] !== undefined) {
                return this.orgSelectedCache[org.id];
            }

            const orgPeople = this.getOrgPeople(org.id);
            const result =
                orgPeople.length > 0 &&
                orgPeople.every((person) => this.isPersonSelected(person));

            this.orgSelectedCache[org.id] = result;
            return result;
        },

        getOrgIndeterminate(org) {
            if (this.orgIndeterminateCache[org.id] !== undefined) {
                return this.orgIndeterminateCache[org.id];
            }

            const orgPeople = this.getOrgPeople(org.id);
            const selectedCount = orgPeople.filter((person) =>
                this.isPersonSelected(person)
            ).length;

            const result =
                selectedCount > 0 && selectedCount < orgPeople.length;
            this.orgIndeterminateCache[org.id] = result;
            return result;
        },

        toggleOrgSelection(org) {
            const orgPeople = this.getOrgPeople(org.id);
            const isSelected = this.isOrgSelected(org);

            if (isSelected) {
                const peopleToRemove = orgPeople.filter((person) =>
                    this.isPersonSelected(person)
                );

                peopleToRemove.forEach((person) => {
                    const index = this.selectedPeople.findIndex(
                        (p) => p.id === person.id
                    );
                    if (index > -1) {
                        this.selectedPeople.splice(index, 1);
                        this.selectedPeopleSet.delete(person.id);
                    }
                });
            } else {
                const peopleToAdd = orgPeople.filter(
                    (person) => !this.isPersonSelected(person)
                );

                this.selectedPeople.push(...peopleToAdd);
                peopleToAdd.forEach((person) => {
                    this.selectedPeopleSet.add(person.id);
                });
            }

            delete this.orgSelectedCache[org.id];
            delete this.orgIndeterminateCache[org.id];
        },

        handleClose() {
            this.dialogVisible = false;
        },

        handleConfirm() {
            this.$emit('input', this.selectedPeople);
            this.$emit('confirm', this.selectedPeople);
            this.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.people-tree-container {
    max-height: 500px;
    overflow-y: auto;
}

.tree-node {
    .node-content {
        display: flex;
        align-items: center;
        padding: 8px 0;
    }

    &.org-node {
        .node-content {
            padding-left: 20px;
        }
    }
}

.people-grid-container {
    padding: 8px 0 8px 40px;

    .virtual-people-list {
        height: 300px;
        overflow-y: auto;
    }
}

.person-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    cursor: pointer;
    height: 32px;
    font-size: 12px;
    border-bottom: 1px solid #f0f0f0;

    .person-checkbox {
        margin-right: 6px;
    }

    .person-name {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &:hover {
        background-color: #f5f7fa;
    }
}

.dialog-footer {
    text-align: right;
    padding-top: 16px;
}
</style>
