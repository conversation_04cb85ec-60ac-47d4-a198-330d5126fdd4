<!-- 会议查询 -->
<template>
    <div class="meeting-search-container">
        <div class="flex" style="margin-bottom: 10px">
            <el-checkbox-group
                class="query-container"
                v-model="type"
                @change="handleCheckboxChange"
            >
                <el-checkbox :label="1">我组织的</el-checkbox>
                <el-checkbox :label="2">我参加的</el-checkbox>
                <el-checkbox :label="3">我提出过要求的</el-checkbox>
                <el-checkbox :label="4">会议任务责任人是我的</el-checkbox>
            </el-checkbox-group>

            <el-checkbox v-model="meetingMinutesFlag" style="margin-left: 100px"
                >有会议纪要</el-checkbox
            >
            <div style="margin-left: auto">
                <el-button class="action-button" type="primary" @click="getList"
                    >查询</el-button
                >
                <el-button
                    class="action-button"
                    type="primary"
                    @click="handleReset"
                    >重置</el-button
                >
            </div>
        </div>
        <div class="flex">
            <div class="query-container">
                <el-select
                    class="query meeting-type"
                    v-model="meetingType"
                    placeholder="会议类型"
                    clearable
                >
                    <el-option
                        v-for="item in CONSTANTS.MEETING_TYPE"
                        :key="item"
                        :label="item"
                        :value="item"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="query meeting-status"
                    v-model="meetingStatus"
                    placeholder="会议状态"
                    clearable
                >
                    <el-option
                        v-for="item in CONSTANTS.MEETING_STATUS"
                        :key="item"
                        :label="item"
                        :value="item"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="query"
                    v-model="meetingMinutesStatus"
                    placeholder="会议纪要状态"
                    clearable
                >
                    <el-option
                        v-for="item in CONSTANTS.MEETING_MINUTES_STATUS"
                        :key="item"
                        :label="item"
                        :value="item"
                    >
                    </el-option>
                </el-select>
                <el-date-picker
                    class="meeting-time"
                    v-model="daterange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <el-select
                    class="query related-project"
                    v-model="projectId"
                    placeholder="关联项目"
                    :title="relatedProjectTitle"
                    remote
                    :remote-method="remoteMethod"
                    filterable
                    clearable
                >
                    <el-option
                        v-for="item in searchOptions"
                        :key="item.projectId"
                        :label="item.projectName"
                        :value="item.projectId"
                    >
                    </el-option>
                </el-select>
                <el-input
                    class="query meeting-title"
                    v-model="meetingTitle"
                    placeholder="会议名称关键字"
                ></el-input>
            </div>
        </div>
        <el-table
            class="meeting-search-table"
            :data="tableList"
            :header-cell-style="{
                'text-align': 'center',
                'border': '1px solid #8c8c8c'
            }"
            :cell-style="{ border: '1px solid #8c8c8c!important' }"
            height="calc(100vh - 240px)"
            empty-text="无会议"
        >
            <el-table-column
                align="left"
                prop="meetingTitle"
                label="会议名称"
                :show-overflow-tooltip="true"
            >
                <template slot-scope="scope">
                    <div>
                        <el-button
                            class="ellipsis-text"
                            type="text"
                            @click="handleMeetingClick(scope.row)"
                            :style="{
                                'max-width': isMinutesFinished(scope.row)
                                    ? 'calc(100% - 15px)'
                                    : '100%'
                            }"
                        >
                            {{ scope.row.meetingTitle }}
                        </el-button>
                        <i
                            v-if="isMinutesFinished(scope.row)"
                            class="el-icon-document"
                            style="color: #3370ff"
                        ></i>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                prop="meetingType"
                label="会议类型"
                align="center"
                width="150"
            >
            </el-table-column>
            <el-table-column
                prop="meetingStatus"
                label="会议状态"
                align="center"
                width="120"
            >
            </el-table-column>
            <el-table-column
                prop="startTime"
                label="会议时间"
                align="center"
                width="150"
            >
            </el-table-column>
            <el-table-column
                prop="minutesStatus"
                label="会议纪要状态"
                align="center"
                width="180"
            >
            </el-table-column>
        </el-table>
        <pagination
            class="pagination"
            :total="total"
            :page.sync="page"
            :limit.sync="limit"
            @pagination="getList"
        />
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants.js';
import Pagination from 'wtf-core-vue/src/components/Pagination';
import { getUserAccount } from '../../commonFunction';

export default {
    name: 'MeetingSearch',
    components: { Pagination },
    data() {
        return {
            pickerOptions: {
                firstDayOfWeek: 1
            },
            // 时间范围
            daterange: [],
            // 和我相关的会议1-组织，2-参加，3-提出要求，4-任务责任人
            type: [],
            // 会议类型
            meetingType: '',
            // 是否有会议纪要
            meetingMinutesFlag: false,
            // 会议纪要状态
            meetingMinutesStatus: '',
            // 会议状态
            meetingStatus: '',
            // 会议名称
            meetingTitle: '',
            // 关联项目ID
            projectId: '',
            // 所有关联项目
            relatedProjectsOptions: [],
            searchOptions: [],
            CONSTANTS,
            tableList: [],
            dateArr: [],
            total: 0,
            page: 1,
            limit: 50
        };
    },
    computed: {
        // 关联项目的hover显示
        relatedProjectTitle() {
            if (!this.projectId) return '';
            return this.searchOptions.find(
                (i) => i.projectId === this.projectId
            ).projectName;
        }
    },
    watch: {
        week(newVal) {
            this.getData();
        }
    },
    created() {
        this.getRelatedProjectsOptions();
        this.getList();
    },
    activated() {
        this.getRelatedProjectsOptions();
        this.getList();
    },
    methods: {
        handleReset() {
            this.daterange = [];
            this.meetingMinutesFlag = false;
            this.meetingMinutesStatus = '';
            this.meetingStatus = '';
            this.meetingTitle = '';
            this.meetingType = '';
            this.projectId = '';
            this.type = [];
            this.page = 1;
            this.limit = 50;
            this.getList();
        },
        async getList() {
            const api = this.$service.feature.meeting.getMeetingList;
            const params = {
                currentPage: this.page,
                startDate: this.daterange ? this.daterange[0] : '',
                endDate: this.daterange ? this.daterange[1] : '',
                myAccount: getUserAccount(this),
                pageSize: this.limit,
                meetingMinutesFlag: this.meetingMinutesFlag ? '是' : '',
                meetingMinutesStatus: this.meetingMinutesStatus,
                meetingStatus: this.meetingStatus,
                meetingTitle: this.meetingTitle,
                meetingType: this.meetingType,
                projectId: this.projectId,
                type: this.type[0] || ''
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.tableList = res.body.list;
                    this.total = res.body.total;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 点击快捷按钮的处理
         * @param {String} type 按钮类型
         */
        handleQuickAccess(type) {
            const currentDate = new Date();
            if (type === '本周') {
                this.week = currentDate;
            } else if (type === '下周') {
                const nextWeekDate = new Date();
                nextWeekDate.setDate(currentDate.getDate() + 7);
                this.week = nextWeekDate;
            }
        },
        handleMeetingClick(row) {
            this.$router.push({
                path: 'meetingDetail',
                query: { id: row.meetingId }
            });
        },
        handleCheckboxChange() {
            if (this.type.length > 1) {
                this.type.splice(0, 1);
            }
        },
        /**
         * 获取所有项目用于下拉框选项
         */
        async getRelatedProjectsOptions() {
            try {
                const api =
                    this.$service.department.naturalResources.getProjectselect;
                const res = await api();
                if (res.head.code === '000000') {
                    this.relatedProjectsOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * 远程搜索，解决过量数据卡顿问题
         * @param {Object} query 参数
         */
        remoteMethod(query) {
            if (query !== '') {
                this.searchOptions = this.relatedProjectsOptions.filter(
                    (item) => {
                        return item.projectName.indexOf(query) > -1;
                    }
                );
            } else {
                this.searchOptions = [];
            }
        },
        /**
         * 判断会议是否已结束
         * @param {Object} row 每行数据
         * @return {Boolean} 会议是否已结束
         */
        isMinutesFinished(row) {
            return (
                row.minutesStatus === '未关闭（有任务未完成）' ||
                row.minutesStatus === '全部关闭' ||
                row.minutesStatus === '审核中'
            );
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.meeting-search-container {
    height: calc(100vh - 55px);
}

.query-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.meeting-type {
    width: 150px;
}
.meeting-status {
    width: 150px;
}
.meeting-time {
    width: 240px;
}
.meeting-title {
    width: 180px;
}
@media (max-width: 568px) {
    .query {
        flex: 1 1 100%;
    }
}
// 修改placeholder颜色
::v-deep .query .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685);
}
.qucik-access {
    margin-left: 10px;
}
.action-button {
    height: 28px;
    margin-left: 15px;
}

.meeting-search-table {
    margin-top: 10px;
    border: 1px solid #8c8c8c !important;
}

.pagination {
    padding: 5px 0;
    margin: 10px 0 0 0;
}

.ellipsis-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    padding: 0px !important;
    height: 23px;
    font-size: 14px;
}

.related-project {
    width: 200px;
    ::v-deep .el-input__inner {
        overflow: hidden !important;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 200px;
    }
}

// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
